#ifndef __MAX30102_H
#define __MAX30102_H

typedef unsigned char uint8_t;
typedef unsigned short uint16_t;
typedef unsigned int uint32_t;

typedef signed char int8_t;
typedef signed short int16_t;
typedef signed int int32_t;

uint32_t MAX30102_WriteReg(uint8_t reg_addr, uint8_t reg_val);
uint32_t MAX30102_ReadReg(uint8_t reg_addr, uint8_t *reg_val);
void MAX30102_Init(void);
void MAX30102_read_fifo(int *pun_red_led, int *pun_ir_led);
void MAX30102_reset(void);
int hr_int(void);
void MAX_Task(void);

extern _Bool wear_flag;//佩戴标志位

#endif
