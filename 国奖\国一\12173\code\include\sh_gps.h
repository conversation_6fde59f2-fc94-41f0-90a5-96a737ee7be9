#ifndef SH_GPS_H
#define SH_GPS_H

#include <stdio.h>
#include <unistd.h>
#include <string.h>
#include <stdlib.h>


#include "ohos_init.h"
#include "cmsis_os2.h"
#include "iot_gpio.h"
#include "iot_uart.h"
#include "hi_uart.h"
#include "iot_watchdog.h"
#include "iot_errno.h"
#include "sh_iot.h"

//定义接收来自GPS数据的数组长度1024
#define UART_BUFF_SIZE 1024
#define U_SLEEP_TIME   700000

extern unsigned char longitude[64];//经度
extern unsigned char latitude[64]; //纬度

void Uart2_Init(void);


unsigned char GPS_Process(unsigned char* uartReadBuff);


#endif
