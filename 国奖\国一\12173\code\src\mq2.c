#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <math.h>
#include <hi_types_base.h>
#include "cmsis_os2.h"
#include "iot_errno.h"
#include "iot_uart.h"
#include "ohos_init.h"
#include "hi_io.h"
#include "hi_adc.h"
#include "iot_gpio.h"

#include "sh_iot.h"

#define THREAD_STACK_SIZE (1024 * 4)
#define THREAD_PRIO 25

//测量的是CO
#define RL 10      // RL阻值
#define R0 26      // R0阻值
_Bool co_flag;   //co超标flag

float mq2_density; // 浓度值
float mq2_voltage;//电压值
// float R00;

// 读取MQ2传感器的电压值
float Smog_Get_Vol(void)
{
    unsigned short temp_val = 0;
    unsigned short v_sum=0;
    unsigned char t;
    unsigned short adc_value = 0;
    float voltage = 0;

    hi_adc_read(HI_ADC_CHANNEL_4, &adc_value, HI_ADC_EQU_MODEL_4, HI_ADC_CUR_BAIS_DEFAULT, 0);

    for (t = 0; t < 10; t++)
    {
        temp_val = adc_value;
        v_sum += temp_val; 
    }
    usleep(5);

    voltage = (3.3 / 4096.0) * (float)(v_sum / 10);   //满电压为3.3v
    return voltage;
}

//计算Smog_ppm
float Smog_GetPPM(void)
{
    float RS = (3.3f - Smog_Get_Vol()) / Smog_Get_Vol() * RL;
    float ppm = 98.322f * pow(RS  / R0, -1.458f);
    return ppm;
}

void MQ2_Task(void)
{
    while (1)
    {
        // mq2_voltage = Smog_Get_Vol();
        // usleep(200000);
        // printf("mq2_voltage=%.2fv\n", mq2_voltage);

        

        mq2_density = Smog_GetPPM();
        

        if (mq2_density > 100)
            co_flag = 1;
        else
            co_flag = 0;

        sensorData.density = ((int)(mq2_density * 100) / 100.0);

        // printf("mq2_density=%.2fppm\n", mq2_density);
        // R00 = MQ7_PPM_Calibration();
        // printf("R00=%f\n", R00);
    }
}

    // 传感器校准函数，根据当前环境PPM值与测得的RS电压值，反推出R0值。
    // 在空气中运行过后测出R0为26
    /* float MQ7_PPM_Calibration()
    {
        float RS = 0;
        float R0 = 0;
        RS = (3.3f - Smog_Get_Vol()) / Smog_Get_Vol() * RL; // RL	10  // RL阻值
        R0 = RS / pow(10 / 98.322, 1 / -1.458f);            // CAL_PPM  10  // 校准环境中PPM值
        return R0;
    } */
