// I2C 地址
#define MAX30102_WRITE_ADDR 0xAE
#define MAX30102_READ_ADDR 0xAF

// 寄存器地址
#define REG_INTR_STATUS_1 0x00   // 中断状态寄存器1
#define REG_INTR_STATUS_2 0x01   // 中断状态寄存器2
#define REG_INTR_ENABLE_1 0x02   // 中断允许寄存器1
#define REG_INTR_ENABLE_2 0x03   // 中断允许寄存器2
#define REG_FIFO_WR_PTR 0x04     // FIFO缓存区写指针寄存器
#define REG_OVF_COUNTER 0x05     // FIFO缓存区溢出计数器寄存器
#define REG_FIFO_RD_PTR 0x06     // FIFO缓存区读指针寄存器
#define REG_FIFO_DATA 0x07       // FIFO缓存区数据寄存器
#define REG_FIFO_CONFIG 0x08     // FIFO缓存区设置寄存器
#define REG_MODE_CONFIG 0x09     // 模式配置寄存器
#define REG_SPO2_CONFIG 0x0A     // 血氧配置寄存器
#define REG_LED1_PA 0x0C         // LED1电流设置寄存器
#define REG_LED2_PA 0x0D         // LED2电流设置寄存器
#define REG_PILOT_PA 0x10        // 探针驱动电流设置寄存器
#define REG_MULTI_LED_CTRL1 0x11 // 多LED控制寄存器1
#define REG_MULTI_LED_CTRL2 0x12 // 多LED控制寄存器2
#define REG_TEMP_INTR 0x1F       // 温度中断阈值寄存器
#define REG_TEMP_FRAC 0x20       // 温度小数部分寄存器
#define REG_TEMP_CONFIG 0x21     // 温度配置寄存器
#define REG_PROX_INT_THRESH 0x30 // 近光阈值寄存器
#define REG_REV_ID 0xFE          // Revision ID寄存器
#define REG_PART_ID 0xFF         // Part ID寄存器