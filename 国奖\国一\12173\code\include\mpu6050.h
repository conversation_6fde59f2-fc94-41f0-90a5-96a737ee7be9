#ifndef __MPU6050_H
#define __MPU6050_H

typedef unsigned char uint8_t;
typedef unsigned short uint16_t;
typedef unsigned int uint32_t;

typedef signed char int8_t;
typedef signed short int16_t;

uint32_t MPU6050_WriteReg(uint8_t reg_addr, uint8_t reg_val);
uint32_t MPU6050_ReadReg(uint8_t reg_addr, uint8_t *reg_val);
void MPU6050_Init(void);
void MPU6050_GetData(float *AccX, float *AccY, float *AccZ, float *GyroX, float *GyroY, float *GyroZ, float *Temperature);

#endif
