#include "sh_gps.h"

unsigned char longitude[64]="123N";//经度
unsigned char latitude[64]="456E"; //纬度

void Uart2_Init(void)
{
    hi_io_set_func(11, 2);
    hi_io_set_func(12, 2);

    uint32_t ret;

    /* 初始化UART配置，波特率 9600，数据bit为8,停止位1，奇偶校验为NONE */
    /* Initialize UART configuration, baud rate is 9600, data bit is 8, stop bit is 1, parity is NONE */
    IotUartAttribute uart_attr = {
        .baudRate = 9600,
        .dataBits = 8,
        .stopBits = 1,
        .parity = 0,
    };

    ret = IoTUartInit(2, &uart_attr);

    if (ret != IOT_SUCCESS) {
        printf("Init Uart1 Falied Error No : %d\n", ret);
        return;
    }

}


unsigned char GPS_Process(unsigned char* uartReadBuff)
{
    char *start = strstr(uartReadBuff, "$GNGGA");
    char *end = strstr(uartReadBuff, "\r\n$GNGLL");
    //printf("---GPS---\n");
    if (start == NULL || end == NULL) 
    {
       // printf("[GPS]$GNGGA line NOT found\n");
    }
    else
    {
        //printf("[GPS]$GNGGA line found\n");
        char gngga[100];
        strncpy(gngga, start, end - start);
        printf("---%s\n",gngga);
        char *token;
        token = strtok(gngga, ",");
        char *nmea_fields[15];
        int i = 0;
        while (token != NULL) 
        {
            nmea_fields[i] = token;
            token = strtok(NULL, ",");
            i++;
            if (i >= 15) break;
        }
        if (i > 6) 
        {  
            printf("[GPS]data found\n");
            sprintf(longitude,"%s %s",nmea_fields[2], nmea_fields[3]);
            sprintf(latitude,"%s %s",nmea_fields[4], nmea_fields[5]);
            strcpy(sensorData.longitude ,longitude);
            strcpy(sensorData.latitude ,latitude);


            printf("[GPS]%s %s,%s %s\n", nmea_fields[2], nmea_fields[3], nmea_fields[4], nmea_fields[5]);
        } else { printf("[GPS]data NOT found\n"); }
    }
    //printf("---GPS_END---\r\n");
}


