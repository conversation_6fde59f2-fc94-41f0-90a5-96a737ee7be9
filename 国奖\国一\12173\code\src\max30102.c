#include "max30102_Reg.h"
#include "max30102.h"
#include "algorithm.h"
#include "alltypes.h"
#include <stdio.h>
#include <unistd.h>
#include "iot_gpio.h"
#include "iot_i2c.h"
#include "ohos_init.h"
#include "cmsis_os2.h"
#include "hi_gpio.h"
#include "hi_io.h"
#include "hi_i2c.h"
#include "iot_errno.h"
#include "iot_i2c_ex.h"

#include "sh_iot.h"

#define I2C_DATA_RATE (400 * 1000) // 定义I2C总线数据传输速率为 400K
#define MAX30102_I2C_IDX HI_I2C_IDX_0

typedef unsigned char uint8_t;
typedef unsigned short uint16_t;
typedef unsigned int uint32_t;

typedef signed char int8_t;
typedef signed short int16_t;

#define MAX_BRIGHTNESS 255

//配带标志位
_Bool wear_flag;
//心率
uint8_t heart_rate;

int aun_ir_buffer[500]; // IR LED sensor data
int n_ir_buffer_length;  // data length
int aun_red_buffer[500]; // Red LED sensor data
int n_sp02;              // SPO2 value
char ch_spo2_valid;      // indicator to show if the SP02 calculation is valid
int n_heart_rate;        // heart rate value
char ch_hr_valid;        // indicator to show if the heart rate calculation is valid
char uch_dummy;


void gpio_init(void)
{
    IoTGpioInit(2);
    IoTGpioInit(13);
    IoTGpioInit(14);
    IoTGpioInit(7);
    IoTGpioSetDir(7, IOT_GPIO_DIR_OUT);
    IoTGpioSetOutputVal(7, 0);
    hi_io_set_func(HI_IO_NAME_GPIO_2, HI_IO_FUNC_GPIO_2_GPIO);
    hi_gpio_set_dir(HI_GPIO_IDX_2, HI_GPIO_DIR_IN);
    hi_io_set_func(HI_IO_NAME_GPIO_13, HI_IO_FUNC_GPIO_13_I2C0_SDA); // 设置GPIO13为I2C数据线
    hi_io_set_func(HI_IO_NAME_GPIO_14, HI_IO_FUNC_GPIO_14_I2C0_SCL); // 设置GPIO14为I2C时钟线

    hi_i2c_init(MAX30102_I2C_IDX, I2C_DATA_RATE); // 初始化I2C总线，使用 I2C0 并设置传输速率
}

uint32_t MAX30102_WriteReg(uint8_t reg_addr, uint8_t reg_val)
{
    uint8_t buffer[2];

    buffer[0] = reg_addr;
    buffer[1] = reg_val;

    uint32_t retval = IoTI2cWrite(MAX30102_I2C_IDX, MAX30102_WRITE_ADDR, buffer, 2);
    if (retval != IOT_SUCCESS)
    {
        printf("IoTI2cWrite failed\n");
        return retval;
    }
    return IOT_SUCCESS;
}

uint32_t MAX30102_ReadReg(uint8_t reg_addr, uint8_t *reg_val)
{
    uint32_t status;

    status = IoTI2cWrite(MAX30102_I2C_IDX, MAX30102_WRITE_ADDR, &reg_addr, 1);
    if (status != IOT_SUCCESS)
    {
        printf("IOTI2cRead phase1 failed\n");
        return 0;
    }

    status = IoTI2cRead(MAX30102_I2C_IDX, MAX30102_READ_ADDR, reg_val, 1);
    if (status != IOT_SUCCESS)
    {
        printf("IOTI2cRead phase2 failed\n");
        return 0;
    }
    return status;
}

void MAX30102_Init(void)
{
    // 分别对应配置 MAX30102 寄存器的不同参数值
    MAX30102_WriteReg(REG_INTR_ENABLE_1, 0xc0); // INTR setting

    MAX30102_WriteReg(REG_INTR_ENABLE_2, 0x00);

    MAX30102_WriteReg(REG_FIFO_WR_PTR, 0x00); // FIFO_WR_PTR[4:0]

    MAX30102_WriteReg(REG_OVF_COUNTER, 0x00); // OVF_COUNTER[4:0]

    MAX30102_WriteReg(REG_FIFO_RD_PTR, 0x00); // FIFO_RD_PTR[4:0]

    MAX30102_WriteReg(REG_FIFO_CONFIG, 0x0f); // sample avg = 1, fifo rollover=false, fifo almost full = 17

    MAX30102_WriteReg(REG_MODE_CONFIG, 0x03); // 0x02 for Red only, 0x03 for SpO2 mode 0x07 multimode LED

    MAX30102_WriteReg(REG_SPO2_CONFIG, 0x27); // SPO2_ADC range = 4096nA, SPO2 sample rate (100 Hz), LED pulseWidth (400uS)

    MAX30102_WriteReg(REG_LED1_PA, 0x24); // Choose value for ~ 7mA for LED1

    MAX30102_WriteReg(REG_LED2_PA, 0x24); // Choose value for ~ 7mA for LED2

    MAX30102_WriteReg(REG_PILOT_PA, 0x7f); // Choose value for ~ 25mA for Pilot LED
}

// 读取 MAX30102 FIFO 数据
void MAX30102_read_fifo(int *pun_red_led, int *pun_ir_led)
{
    // 定义返回值和发送、接收数据的缓存
    int un_temp;
    int ret = -1;
    *pun_red_led = 0;
    *pun_ir_led = 0;
    unsigned char uch_temp;

    unsigned char sendbuf[1] = {0};
    unsigned char rcvbuf[6] = {0};

    // 定义 Hi_i2c_data 结构体，方便进行 I2C 读写操作
    hi_i2c_data i2c_data = {0};
    i2c_data.send_buf = sendbuf;
    i2c_data.send_len = sizeof(sendbuf);
    i2c_data.receive_buf = rcvbuf;
    i2c_data.receive_len = sizeof(rcvbuf);

    // 读取和清除状态寄存器
    MAX30102_ReadReg(REG_INTR_STATUS_1, &uch_temp);
    MAX30102_ReadReg(REG_INTR_STATUS_2, &uch_temp);

    // 将 FIFO 数据寄存器地址存入缓存
    sendbuf[0] = REG_FIFO_DATA;

    // 发送 I2C 数据，并判断是否发送成功
    hi_i2c_write(0, MAX30102_WRITE_ADDR, &i2c_data);

    // 读取 I2C 数据，并判断是否读取成功
    hi_i2c_read(0, MAX30102_READ_ADDR, &i2c_data);

    // 将接收到的数据转换为赤红光和红外光的值
    un_temp = (unsigned char)rcvbuf[0];
    un_temp <<= 16;
    *pun_red_led += un_temp;
    un_temp = (unsigned char)rcvbuf[1];
    un_temp <<= 8;
    *pun_red_led += un_temp;
    un_temp = (unsigned char)rcvbuf[2];
    *pun_red_led += un_temp;

    un_temp = (unsigned char)rcvbuf[3];
    un_temp <<= 16;
    *pun_ir_led += un_temp;
    un_temp = (unsigned char)rcvbuf[4];
    un_temp <<= 8;
    *pun_ir_led += un_temp;
    un_temp = (unsigned char)rcvbuf[5];
    *pun_ir_led += un_temp;
    *pun_red_led &= 0x03FFFF; // Mask MSB [23:18]
    *pun_ir_led &= 0x03FFFF;  // Mask MSB [23:18]
}

// MAX30102 软件复位
void MAX30102_reset(void)
{
    MAX30102_WriteReg(REG_MODE_CONFIG,0x40);
    // MAX30102_WriteReg(REG_MODE_CONFIG,0x40);
}

int hr_int(hi_void)
{
    int ret = 0;
    hi_gpio_value gpio2_val = HI_GPIO_VALUE0;
    ret = hi_gpio_get_input_val(HI_GPIO_IDX_2, &gpio2_val);
    if (ret != HI_ERR_SUCCESS)
    {
        printf("===== ERROR ===== gpio -> hi_gpio_get_input_val ret:%d\r\n", ret);
        return 0;
    }

    return gpio2_val;
}

void MAX_Task(void)
{
    uint8_t max_abnormal=0, max_normal=0;

    gpio_init(); // 初始化gpio

    IoTWatchDogDisable(); // 关闭看门狗

    int un_min, un_max, un_prev_data=0; // 用于计算反映心跳的板载 LED 亮度的变量
    int i=0;
    int n_brightness=0;
    float f_temp=0;
    int ret=0;

    MAX30102_reset(); // 复位 MAX30102
    sleep(1);

    MAX30102_ReadReg(0, &uch_dummy); // 读取并清除状态寄存器

    MAX30102_Init(); // 初始化 MAX30102

    n_brightness = 0; // LED 亮度初始化为 0
    un_min = 0x3FFFF; // 红光传感器数据最小值初始化为 最大值
    un_max = 0;       // 红光传感器数据最大值初始化为 0

    n_ir_buffer_length = 500; // 缓冲区长度为 500，存储运行于 100sps 的样本的 5 秒钟的数据

    // 读取前 500 个样本，确定信号范围
    for (i = 0; i < n_ir_buffer_length; i++)
    {
        ret = hr_int();
        while (ret == 1)
        {
            ret = hr_int();
        } // 等待直到中断引脚断言

        MAX30102_read_fifo((aun_red_buffer + i), (aun_ir_buffer + i)); // 从 MAX30102 FIFO 缓存区中读取

        if (un_min > aun_red_buffer[i])
            un_min = aun_red_buffer[i]; // 更新信号最小值
        if (un_max < aun_red_buffer[i])
            un_max = aun_red_buffer[i]; // 更新信号最大值
    }
    un_prev_data = aun_red_buffer[i];

    // 在读取前 500 个样本后计算心率和 SpO2(即前 5 秒钟的样本)
    maxim_heart_rate_and_oxygen_saturation(aun_ir_buffer, n_ir_buffer_length, aun_red_buffer, &n_sp02, &ch_spo2_valid, &n_heart_rate, &ch_hr_valid);

    // 持续从 MAX30102 中采集样本，每 1 秒计算一次心率和 SpO2
    while (1)
    {
        i = 0;
        un_min = 0x3FFFF; // 同上
        un_max = 0;       // 同上

        // 把内存中的前 100 组样本丢弃，并把后面的 400 组样本移到最前面
        for (i = 100; i < 500; i++)
        {
            aun_red_buffer[i - 100] = aun_red_buffer[i]; // 将红光传感器数据中前 100 组样本丢弃
            aun_ir_buffer[i - 100] = aun_ir_buffer[i];   // 将红外线传感器数据中前 100 组样本丢弃

            // 更新最小值和最大值
            if (un_min > aun_red_buffer[i])
                un_min = aun_red_buffer[i]; // 更新最小信号值
            if (un_max < aun_red_buffer[i])
                un_max = aun_red_buffer[i]; // 更新最大信号值
        }

        // 取出后 100 组样本，然后计算心率
        for (i = 400; i < 500; i++)
        {
            ret = 0;
            un_prev_data = aun_red_buffer[i - 1];

            ret = hr_int();
            while (ret == 1)
            {
                ret = hr_int();
            } // 等待直到中断引脚断言

            MAX30102_read_fifo((aun_red_buffer + i), (aun_ir_buffer + i)); // 从 MAX30102 FIFO 缓存区中读取数据

            if (aun_red_buffer[i] > un_prev_data) // 根据相邻两个 AD 数据的偏差确定 LED 亮度
            {
                f_temp = aun_red_buffer[i] - un_prev_data;
                f_temp /= (un_max - un_min);
                f_temp *= MAX_BRIGHTNESS;
                n_brightness -= (int)f_temp;
                if (n_brightness < 0)
                    n_brightness = 0;
            }
            else
            {
                f_temp = un_prev_data - aun_red_buffer[i];
                f_temp /= (un_max - un_min);
                f_temp *= MAX_BRIGHTNESS;
                n_brightness += (int)f_temp;
                if (n_brightness > MAX_BRIGHTNESS)
                    n_brightness = MAX_BRIGHTNESS;
            }
        }
        // 通过已经采集的数据计算心率和 SpO2
        maxim_heart_rate_and_oxygen_saturation(aun_ir_buffer, n_ir_buffer_length, aun_red_buffer, &n_sp02, &ch_spo2_valid, &n_heart_rate, &ch_hr_valid);



        if (aun_ir_buffer[i] < 10000) // 不佩戴状态
        {
            max_abnormal++;
            if (max_abnormal >= 5)//6
            {
                wear_flag = 0;
                max_abnormal = 0;
                IoTGpioSetOutputVal(7, 1);
                usleep(1000);//1000
                IoTGpioSetOutputVal(7, 0);
            }
        }
        else
        {
            max_abnormal = 0;
            wear_flag = 1;
            IoTGpioSetOutputVal(7, 0);
        }
        usleep(6000); // 延时6000
       
       
        if (n_heart_rate < 120 && n_heart_rate > 60)
            heart_rate = n_heart_rate;

        sensorData.heartrate = heart_rate;


        printf("ir=%i \n", aun_ir_buffer[i]);
        printf("wear_flag=%d\n",wear_flag);
        printf("heart_rate=%d\n", heart_rate);
    }
}
