#ifndef __MPU6050_REG_H
#define __MPU6050_REG_H

// 设置采样率分频寄存器地址
#define MPU6050_SMPLRT_DIV      0x19
// 配置寄存器地址
#define MPU6050_CONFIG          0x1A
// 陀螺仪配置寄存器地址
#define MPU6050_GYRO_CONFIG     0x1B
// 加速度计配置寄存器地址
#define MPU6050_ACCEL_CONFIG    0x1C

// X轴加速度值高字节寄存器地址
#define MPU6050_ACCEL_XOUT_H    0x3B
// X轴加速度值低字节寄存器地址
#define MPU6050_ACCEL_XOUT_L    0x3C
// Y轴加速度值高字节寄存器地址
#define MPU6050_ACCEL_YOUT_H    0x3D
// Y轴加速度值低字节寄存器地址
#define MPU6050_ACCEL_YOUT_L    0x3E
// Z轴加速度值高字节寄存器地址
#define MPU6050_ACCEL_ZOUT_H    0x3F
// Z轴加速度值低字节寄存器地址
#define MPU6050_ACCEL_ZOUT_L    0x40
// 温度值高字节寄存器地址
#define MPU6050_TEMP_OUT_H      0x41
// 温度值低字节寄存器地址
#define MPU6050_TEMP_OUT_L      0x42
// X轴陀螺仪值高字节寄存器地址
#define MPU6050_GYRO_XOUT_H     0x43
// X轴陀螺仪值低字节寄存器地址
#define MPU6050_GYRO_XOUT_L     0x44
// Y轴陀螺仪值高字节寄存器地址
#define MPU6050_GYRO_YOUT_H     0x45
// Y轴陀螺仪值低字节寄存器地址
#define MPU6050_GYRO_YOUT_L     0x46
// Z轴陀螺仪值高字节寄存器地址
#define MPU6050_GYRO_ZOUT_H     0x47
// Z轴陀螺仪值低字节寄存器地址
#define MPU6050_GYRO_ZOUT_L     0x48

// 电源管理1寄存器地址
#define MPU6050_PWR_MGMT_1      0x6B
// 电源管理2寄存器地址
#define MPU6050_PWR_MGMT_2      0x6C
// 器件ID寄存器地址
#define MPU6050_WHO_AM_I        0x75

#endif