#include <stdio.h>
#include <unistd.h>
#include <string.h>
#include <stdlib.h>
#include <math.h>
#include "ohos_init.h"
#include "cmsis_os2.h"

#include "bsp_wifi.h"
#include "bsp_mqtt.h"

#include "sh_iot.h"
#include "sh_gps.h"
#include "mpu6050.h"
#include "max30102.h"
#include "mq2.h"
#include "app_http_client.h"

#include "cJSON.h"

unsigned char WiFi_state = 0;

_Bool fall_flag = 0; // 人体跌倒检测标志位【1：跌倒，0：正常】
uint8_t val=0;//检测语言模块引脚电平，用于取消摔倒报警
static void WiFi_Task(void)
{
    // 连接WiFi
    if (WiFi_connectHotspots("Nikko OPPO", "13426763921") != WIFI_SUCCESS)
    {
        printf("[error] connectWiFiHotspots\r\n");
        WiFi_state = 0;
    }
    else
        WiFi_state = 1;
    sleep(TASK_INIT_TIME);
}

static void IoT_Task(void)
{
    p_MQTTClient_sub_callback = &mqttClient_sub_callback;

    osThreadAttr_t options;
    options.name = "wifi_task";
    options.attr_bits = 0;
    options.cb_mem = NULL;
    options.cb_size = 0;
    options.stack_mem = NULL;
    options.stack_size = TASK_STACK_SIZE;
    options.priority = osPriorityNormal;
    osThreadId_t wifi_task_id = osThreadNew((osThreadFunc_t)WiFi_Task, NULL, &options);
    if (wifi_task_id != NULL)
    {
        printf("ID = %d, Create wifi_task_id is OK!\r\n", wifi_task_id);
    }

    while (WiFi_state != 1)
    {
        sleep(1);
    }

    // 连接MQTT服务器
    if (MQTTClient_connectServer(SERVER_IP_ADDR, SERVER_IP_PORT) != WIFI_SUCCESS)
    {
        printf("[error] mqttClient_connectServer\r\n");
    }
    sleep(TASK_INIT_TIME);

    // 初始化MQTT客户端
    if (MQTTClient_init(MQTT_CLIENT_ID, MQTT_USER_NAME, MQTT_PASS_WORD) != WIFI_SUCCESS)
    {
        printf("[error] mqttClient_init\r\n");
    }
    sleep(TASK_INIT_TIME);

    // 订阅主题
    if (MQTTClient_subscribe(MQTT_TOPIC_SUB_COMMANDS) != WIFI_SUCCESS)
    {
        printf("[error] mqttClient_subscribe\r\n");
    }
    sleep(TASK_INIT_TIME);

    //  创建线程
    options.name = "mqtt_send_task";
    options.stack_size = TASK_STACK_SIZE;
    mqtt_send_task_id = osThreadNew((osThreadFunc_t)mqtt_send_task, NULL, &options);
    if (mqtt_send_task_id != NULL)
    {
        printf("ID = %d, Create mqtt_send_task_id is OK!\r\n", mqtt_send_task_id);
    }

    options.name = "mqtt_recv_task";
    options.stack_size = TASK_STACK_SIZE;
    mqtt_recv_task_id = osThreadNew((osThreadFunc_t)mqtt_recv_task, NULL, &options);
    if (mqtt_recv_task_id != NULL)
    {
        printf("ID = %d, Create mqtt_recv_task_id is OK!\r\n", mqtt_recv_task_id);
    }
}

static void GPS_Task(void)
{
    uint32_t count = 0;
    uint32_t len = 0;
    unsigned char uartReadBuff[UART_BUFF_SIZE] = {0};

    Uart2_Init();

    while (1)
    {

        // 通过UART1 接收数据 Receive data through UART1
        usleep(U_SLEEP_TIME);
        len = IoTUartRead(2, uartReadBuff, UART_BUFF_SIZE);
        if (len > 0)
        {
            GPS_Process(uartReadBuff);
        }
    }
}

static void Part_Task()
{
    IoTGpioGetInputVal(10, &val);
    printf("val=%d", val);
    printf("val=%d", val);
    printf("val=%d", val);
    printf("val=%d", val);
    printf("val=%d", val);
} 

static void MPU_Task(void)
{
    // x轴加速度，y轴加速度，z轴加速度，x轴角速度，y轴角速度，z轴角速度,温度
    float AX, AY, AZ, GX, GY, GZ, Temp;
    //俯仰角（pitch）和横滚角（roll） 
    float pitch_angle, roll_angle;
    //中间计算变量
    float i,j;
    //异常,正常计数值
    uint8_t mpu_abnormal=0, mpu_normal=0;

    MPU6050_Init();
    while (1)
    {
        MPU6050_GetData(&AX, &AY, &AZ, &GX, &GY, &GZ, &Temp);
        sensorData.temperature = ((int)(Temp * 100) / 100.0);

        i = -AX / (sqrt(AY * AY + AZ * AZ));
        j = AY / AZ;
        pitch_angle = atan(i)* 180 / 3.14;
        roll_angle = atan(j) * 180 / 3.14;

        // if (fabs(pitch_angle) > 50 || fabs(roll_angle) > 50)  //摔倒5秒才置位
        // {
        //     mpu_abnormal++;
        //     if (mpu_abnormal >= 10)
        //     {
        //         mpu_abnormal = 0;
        //         fall_flag = 1;
        //         IoTGpioSetOutputVal(8,1);
        //         usleep(30000);
        //         IoTGpioSetOutputVal(8, 0);
        //     }
        // }
        // else                                              
        // {
        //     mpu_abnormal = 0;
        //     fall_flag = 0;
        //     IoTGpioSetOutputVal(8,0);
        // }
        // usleep(125000); // 延时0.05s

        

        if (wear_flag)
        {
            if ((fabs(pitch_angle) > 40 || fabs(roll_angle) > 40) && (val==0))
            // if (fabs(pitch_angle) > 40 || fabs(roll_angle) > 40)
            {
                mpu_abnormal++;
                if (mpu_abnormal >= 25)
                {
                    fall_flag = 1;
                    mpu_abnormal = 0;
                    IoTGpioSetOutputVal(8, 1);
                    usleep(30000);//30000
                    IoTGpioSetOutputVal(8, 0);
                }
            }
            else
            {
                mpu_abnormal = 0;
                fall_flag = 0;
                IoTGpioSetOutputVal(8, 0);
            }
            usleep(200000); // 50000
        }
       
        // printf("AX:%.2f m/s2 \n", AX);
        // printf("AY:%.2f m/s2 \n", AY);
        // printf("AZ:%.2f m/s2 \n", AZ);
        printf("pitch_angle:%.2f \n", pitch_angle);
        printf("roll_angle:%.2f \n", roll_angle);
        printf("mpu_normal:%d \n", mpu_normal);
        printf("mpu_abnormal:%d \n", mpu_abnormal);
        printf("fall_flag:%d \n", fall_flag);
        // printf("GX:%.2f\n", GX);
        // printf("GY:%.2f\n", GY);
        // printf("GZ:%.2f\n", GZ);

        // printf("Temp:%.2f\n", Temp);
    }
}


static void Warning_Task(void)
{
    while (1)
    {
        if (fall_flag == 1)
        {
            char *g_request = "GET /fall HTTP/1.1\r\n\
                                Content-Type: application/x-www-form-urlencoded;charset=UTF-8\r\n\
                                Host: baidu.com\r\n\
                                Connection: close\r\n\
                                \r\n";
            http_clienti_get("*************",g_request);
        }
        if (fall_flag == 0)
        {
            char *g_request = "GET /fall_ok HTTP/1.1\r\n\
                                Content-Type: application/x-www-form-urlencoded;charset=UTF-8\r\n\
                                Host: baidu.com\r\n\
                                Connection: close\r\n\
                                \r\n";
            http_clienti_get("*************",g_request);
        }
        if (wear_flag == 0)
        {
            char *g_request = "GET /wear HTTP/1.1\r\n\
                                Content-Type: application/x-www-form-urlencoded;charset=UTF-8\r\n\
                                Host: baidu.com\r\n\
                                Connection: close\r\n\
                                \r\n";
            http_clienti_get("*************",g_request);
        }
        if (wear_flag == 1)
        {
            char *g_request = "GET /wear_ok HTTP/1.1\r\n\
                                Content-Type: application/x-www-form-urlencoded;charset=UTF-8\r\n\
                                Host: baidu.com\r\n\
                                Connection: close\r\n\
                                \r\n";
            http_clienti_get("*************",g_request);
        }
        usleep(10000);
    }
}

static void Sensor_Task(void)
{
    osThreadAttr_t attr;
    IoTWatchDogDisable();

    attr.name = "GPS_Task";
    attr.attr_bits = 0U;
    attr.cb_mem = NULL;
    attr.cb_size = 0U;
    attr.stack_mem = NULL;
    attr.stack_size = 5 * 1024; // 任务栈大小*1024
    attr.priority = osPriorityNormal;
    osThreadId_t gps_task_id = osThreadNew((osThreadFunc_t)GPS_Task, NULL, &attr);
    if (gps_task_id != NULL)
    {
        printf("ID = %d, Create gps_task_id is OK!\r\n", gps_task_id);
    }

    attr.name = "MPU_Task";
    attr.stack_size = 5 * 1024;
    attr.priority = osPriorityNormal;
    osThreadId_t mpu_task_id = osThreadNew((osThreadFunc_t)MPU_Task, NULL, &attr);
    if (mpu_task_id != NULL)
    {
        printf("ID = %d, Create mpu_task_id is OK!\r\n", mpu_task_id);
    }

    attr.name = "MAX_Task";
    attr.stack_size = 5 * 1024;
    attr.priority = osPriorityNormal;
    osThreadId_t max_task_id = osThreadNew((osThreadFunc_t)MAX_Task, NULL, &attr);
    if (max_task_id != NULL)
    {
        printf("ID = %d, Create max_task_id is OK!\r\n", max_task_id);
    }

    // attr.name = "MQ2_Task";
    // attr.stack_size = 5 * 1024;
    // attr.priority = osPriorityNormal;
    // osThreadId_t mq2_task_id = osThreadNew((osThreadFunc_t)MQ2_Task, NULL, &attr);
    // if (mq2_task_id != NULL)
    // {
    //     printf("ID = %d, Create mq2_task_id is OK!\r\n", mq2_task_id);
    // }

    // attr.name = "Part_Task";
    // attr.stack_size = 5 * 1024;
    // attr.priority = osPriorityNormal;
    // osThreadId_t part_task_id = osThreadNew((osThreadFunc_t)Part_Task, NULL, &attr);
    // if (part_task_id != NULL)
    // {
    //     printf("ID = %d, Create part_task_id is OK!\r\n", part_task_id);
    // }

    attr.name = "Warning_Task";
    attr.stack_size = 5 * 1024;
    attr.priority = osPriorityNormal;
    osThreadId_t warning_task_id = osThreadNew((osThreadFunc_t)Warning_Task, NULL, &attr);
    if (warning_task_id != NULL)
    {
        printf("ID = %d, Create warning_task_id is OK!\r\n", warning_task_id);
    }
}

static void main_task()
{
    IoT_Task();
    Sensor_Task();
}

APP_FEATURE_INIT(main_task);
