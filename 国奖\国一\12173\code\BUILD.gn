static_library("smart_helmet") {
    sources = [
        "main.c",

        "./src/bsp_wifi.c",
        "./src/bsp_mqtt.c",
        "./src/sh_iot.c",
        "./src/sh_gps.c",
        "./src/mpu6050.c",
        "./src/max30102.c",
        "./src/algorithm.c",
        "./src/mq2.c",
        "./src/app_http_client.c",

        "//third_party/paho.mqtt.embedded-c/MQTTPacket/src/MQTTConnectClient.c",
        "//third_party/paho.mqtt.embedded-c/MQTTPacket/src/MQTTConnectServer.c",
        "//third_party/paho.mqtt.embedded-c/MQTTPacket/src/MQTTDeserializePublish.c",
        "//third_party/paho.mqtt.embedded-c/MQTTPacket/src/MQTTFormat.c",
        "//third_party/paho.mqtt.embedded-c/MQTTPacket/src/MQTTPacket.c",
        "//third_party/paho.mqtt.embedded-c/MQTTPacket/src/MQTTSerializePublish.c",
        "//third_party/paho.mqtt.embedded-c/MQTTPacket/src/MQTTSubscribeClient.c",
        "//third_party/paho.mqtt.embedded-c/MQTTPacket/src/MQTTSubscribeServer.c",
        "//third_party/paho.mqtt.embedded-c/MQTTPacket/src/MQTTUnsubscribeServer.c",
        "//third_party/paho.mqtt.embedded-c/MQTTPacket/src/MQTTUnsubscribeClient.c",

    ]

    include_dirs = [
        "//utils/native/lite/include",
        "//kernel/liteos_m/kal/cmsis",
        "//base/iot_hardware/peripheral/interfaces/kits",
        "//foundation/communication/wifi_lite/interfaces/wifiservice",
        "./include",
        "//third_party/paho.mqtt.embedded-c/MQTTPacket/src",
        "//third_party/cJSON",
        "//device/hisilicon/hispark_pegasus/sdk_liteos/platform/os/Huawei_LiteOS/components/lib/libc/musl/include",
        "//device/hisilicon/hispark_pegasus/sdk_liteos/include",
        "//device/hisilicon/hispark_pegasus/hi3861_adapter/kal/cmsis",
        "//device/hisilicon/hispark_pegasus/sdk_liteos/platform/os/Huawei_LiteOS/components/lib/libc/musl/include/bits",
        "//vendor/bearpi/bearpi_hm_nano/common/iot_hardware_hals/include",
    ]
}