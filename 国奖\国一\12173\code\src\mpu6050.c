#include "mpu6050_Reg.h"
#include "alltypes.h"
#include <stdio.h>
#include <unistd.h>
#include "iot_gpio.h"
#include "iot_i2c.h"
#include "ohos_init.h"
#include "cmsis_os2.h"
#include "hi_gpio.h"
#include "hi_io.h"
#include "hi_i2c.h"
#include "iot_errno.h"
#include "iot_i2c_ex.h"

#define MPU6050_WRITE_ADDRESS		0xD0
#define MPU6050_READ_ADDRESS		0xD1

#define I2C_DATA_RATE  (400 * 1000)  // 定义I2C总线数据传输速率为 400K
#define MPU6050_I2C_IDX HI_I2C_IDX_1


uint32_t MPU6050_WriteReg(uint8_t reg_addr, uint8_t reg_val)
{
    uint8_t buffer[2];
    
    buffer[0] = reg_addr;
    buffer[1] = reg_val;

    uint32_t retval = IoTI2cWrite(MPU6050_I2C_IDX, MPU6050_WRITE_ADDRESS, buffer, 2);
    if (retval != IOT_SUCCESS) {
        printf("IoTI2cWrite failed\n");
        return retval;
    }
    return IOT_SUCCESS;
}


uint32_t MPU6050_ReadReg(uint8_t reg_addr, uint8_t *reg_val)
{
    uint32_t status;

    status = IoTI2cWrite(MPU6050_I2C_IDX, MPU6050_WRITE_ADDRESS, &reg_addr, 1);
    if (status != IOT_SUCCESS) {
        printf("IOTI2cRead phase1 failed\n");
        return 0;
    }

    status = IoTI2cRead(MPU6050_I2C_IDX, MPU6050_READ_ADDRESS, reg_val, 1);
    if (status != IOT_SUCCESS) {
        printf("IOTI2cRead phase2 failed\n");
        return 0;
    }
    return status;
}

void MPU6050_Init(void)
{
	IoTGpioInit(0);
	IoTGpioInit(1);
	IoTGpioInit(8);
	IoTGpioSetDir(8, IOT_GPIO_DIR_OUT);
	IoTGpioSetOutputVal(8, 0);
	hi_io_set_func(HI_IO_NAME_GPIO_0, HI_IO_FUNC_GPIO_0_I2C1_SDA);	  // 设置GPIO0为I2C数据线
	hi_io_set_func(HI_IO_NAME_GPIO_1, HI_IO_FUNC_GPIO_1_I2C1_SCL);   // 设置GPIO1为I2C时钟线

    hi_i2c_init(MPU6050_I2C_IDX, I2C_DATA_RATE);   // 初始化I2C总线，使用 I2C0 并设置传输速率

	MPU6050_WriteReg(MPU6050_PWR_MGMT_1, 0x01);
	MPU6050_WriteReg(MPU6050_PWR_MGMT_2, 0x00);
	MPU6050_WriteReg(MPU6050_SMPLRT_DIV, 0x09);
	MPU6050_WriteReg(MPU6050_CONFIG, 0x06);
	MPU6050_WriteReg(MPU6050_GYRO_CONFIG, 0x18);
	MPU6050_WriteReg(MPU6050_ACCEL_CONFIG, 0x18);
}

void MPU6050_GetData(float *AccX, float *AccY, float *AccZ,
					 float *GyroX, float *GyroY, float *GyroZ, float *Temperature)
{
	uint8_t DataH, DataL;
	int16_t AX,AY,AZ,GX,GY,GZ,tempData;

	MPU6050_ReadReg(MPU6050_ACCEL_XOUT_H, &DataH);
	MPU6050_ReadReg(MPU6050_ACCEL_XOUT_L,&DataL);
	AX=(DataH << 8) | DataL;
	*AccX = (double)(AX * 16 * 10) / 32768;

	MPU6050_ReadReg(MPU6050_ACCEL_YOUT_H, &DataH);
	MPU6050_ReadReg(MPU6050_ACCEL_YOUT_L,&DataL);
	AY = (DataH << 8) | DataL;
	*AccY = (double)(AY * 160) / 32768;

	MPU6050_ReadReg(MPU6050_ACCEL_ZOUT_H,&DataH);
	MPU6050_ReadReg(MPU6050_ACCEL_ZOUT_L,&DataL);
	AZ = (DataH << 8) | DataL;
	*AccZ = ((double)(AZ * 160)) / 32768;
	
	MPU6050_ReadReg(MPU6050_GYRO_XOUT_H,&DataH);
	MPU6050_ReadReg(MPU6050_GYRO_XOUT_L,&DataL);
	GX = (DataH << 8) | DataL;
	*GyroX = ((double)(GX * 2000)) / 32768;

	MPU6050_ReadReg(MPU6050_GYRO_YOUT_H,&DataH);
	MPU6050_ReadReg(MPU6050_GYRO_YOUT_L,&DataL);
	GY = (DataH << 8) | DataL;
	*GyroY = ((double)(GY * 2000)) / 32768;
	
	MPU6050_ReadReg(MPU6050_GYRO_ZOUT_H,&DataH);
	MPU6050_ReadReg(MPU6050_GYRO_ZOUT_L,&DataL);
	GZ = (DataH << 8) | DataL;
	*GyroZ = ((double)(GZ * 2000)) / 32768;

	MPU6050_ReadReg(MPU6050_TEMP_OUT_H, &DataH);
	MPU6050_ReadReg(MPU6050_TEMP_OUT_L, &DataL);
	tempData = (DataH << 8) | DataL;
	*Temperature = (((double)(tempData + 13200)) / 280) - 13;
}