#ifndef SH_IOT_H
#define SH_IOT_H


#include <stdio.h>
#include <unistd.h>
#include <string.h>
#include <stdlib.h>

#include "ohos_init.h"
#include "cmsis_os2.h"

#include "bsp_wifi.h"
#include "bsp_mqtt.h"

#include "cJSON.h"


// 设备ID
#define DEVICE_ID "62d819ba4c7c4e3646bba832_H001"
// MQTT客户端ID
#define MQTT_CLIENT_ID "62d819ba4c7c4e3646bba832_H001_0_0_2023070505"
// MQTT用户名
#define MQTT_USER_NAME "62d819ba4c7c4e3646bba832_H001"
// MQTT密码
#define MQTT_PASS_WORD "5d519d9cc1efab60fce0b70a4c9e5350a6f772d0d1ced480e9eb01e1cab88f00"
// 华为云平台的IP地址
#define SERVER_IP_ADDR "*************"
// 华为云平台的IP端口号
#define SERVER_IP_PORT 1883
// 订阅 接收控制命令的主题
#define MQTT_TOPIC_SUB_COMMANDS "$oc/devices/%s/sys/commands/#"
// 发布 成功接收到控制命令后的主题
#define MQTT_TOPIC_PUB_COMMANDS_REQ "$oc/devices/%s/sys/commands/response/request_id=%s"
#define MALLOC_MQTT_TOPIC_PUB_COMMANDS_REQ "$oc/devices//sys/commands/response/request_id="

// 发布 设备属性数据的主题
#define MQTT_TOPIC_PUB_PROPERTIES "$oc/devices/%s/sys/properties/report"
#define MALLOC_MQTT_TOPIC_PUB_PROPERTIES "$oc/devices//sys/properties/report"

#define TASK_STACK_SIZE (1024 * 25)
#define MsgQueueObjectNumber 16 // 定义消息队列对象的个数
typedef struct message_sensorData
{
    uint8_t led;        // LED灯当前的状态
    uint8_t power;      // 剩余电量
    uint16_t state;     // 状态
    uint16_t heartrate; // 心率
    unsigned char longitude[64]; // 经度
    unsigned char latitude[64];  // 纬度
    float temperature;  // 当前的温度值
    float humidity;     // 当前的湿度值
    float density;     // 当前的co浓度值
} msg_sensorData_t;
extern msg_sensorData_t sensorData; // 传感器的数据
extern osThreadId_t mqtt_send_task_id;    // mqtt 发布数据任务ID
extern osThreadId_t mqtt_recv_task_id;    // mqtt 接收数据任务ID
#define MQTT_SEND_TASK_TIME 3      // s
#define MQTT_RECV_TASK_TIME 1      // s
#define TASK_INIT_TIME 2           // s
#define MQTT_DATA_MAX 256
extern uint8_t publish_topic[MQTT_DATA_MAX];
extern uint8_t mqtt_data[MQTT_DATA_MAX];

/**
 * @brief 组JSON数据
 */
int Packaged_json_data(void);

/**
 * @brief MQTT  发布消息任务
 */
void mqtt_send_task(void);

int get_jsonData_value(const cJSON *const object, uint8_t *value);

/**
 * @brief 解析JSON数据
 */
int Parsing_json_data(const char *payload);

// 向云端发送返回值
void send_cloud_request_code(const char *request_id, int ret_code, int request_len);

/**
 * @brief MQTT接收数据的回调函数
 */
int8_t mqttClient_sub_callback(unsigned char *topic, unsigned char *payload);

/**
 * @brief MQTT  接收消息任务
 */
void mqtt_recv_task(void);

#endif